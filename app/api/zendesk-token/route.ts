import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function POST(request: Request) {
    const body = await request.json();
    const { email, name, id } = body;

    if (!email || !name || !id) {
        return NextResponse.json({ error: 'Missing user info' }, { status: 400 });
    }

    const payload = {
        external_id: id,
        name,
        email,
        scope: 'user',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
    };

    const token = jwt.sign(payload, process.env.ZENDESK_SECRET!, {
        algorithm: 'HS256',
    });

    return NextResponse.json({ token });
}
