import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function POST(request: Request) {
    const body = await request.json();
    const { email, name, id } = body;

    if (!email || !id) {
        return NextResponse.json({ error: 'Missing user info' }, { status: 400 });
    }

    // Use email as fallback name if name is not provided
    const userName = name || email;

    const payload = {
        external_id: id,
        name: userName,
        email,
        scope: 'user',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
    };

    const token = jwt.sign(payload, process.env.ZENDESK_SECRET!, {
        algorithm: 'HS256',
    });

    return NextResponse.json({ token });
}
