import { DashboardOptionBar } from '@/components/DashboardOptionBar';
import { SideBar } from '@/components/SideBar';
import { ZendeskChat } from '@/components/ZendeskChat/ZendeskChat';
import { DeploymentsTableTabsProvider } from '@/contexts/DeploymentsTableTabsContext';
import Script from 'next/script';

export default async function DashboardLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <div className="flex flex-row">
            <ZendeskChat />
            <SideBar />
            <div className="w-full ml-52">
                <DeploymentsTableTabsProvider>
                    <div className="p-box-50">
                        <DashboardOptionBar />
                        {children}
                    </div>
                </DeploymentsTableTabsProvider>
            </div>
        </div>
    );
}
