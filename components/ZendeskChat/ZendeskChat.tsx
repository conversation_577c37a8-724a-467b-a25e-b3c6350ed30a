'use client';
import { useUserStore } from '@/store/userStore';
import { useEffect } from 'react';
import axios from 'axios';
import Script from 'next/script';

export const ZendeskChat = () => {
    const { user } = useUserStore();

    useEffect(() => {
        const identifyUser = async () => {
            if (!user) return;

            const { data } = await axios.post('/api/zendesk-token', {
                id: user.id,
                name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || undefined,
                email: user.email,
            });

            const token = data.token;

            const waitForZendesk = () => {
                if (typeof window.zE === 'function') {
                    window.zE('messenger', 'loginUser', { jwt: token });
                } else {
                    setTimeout(waitForZendesk, 100);
                }
            };

            waitForZendesk();
        };

        if (user) {
            identifyUser();
        }
    }, [user]);

    return (
        <>
            <Script
                id="ze-snippet"
                src="https://static.zdassets.com/ekr/snippet.js?key=bbb92d84-74f1-41d0-a90d-74bd558bbc72"
                strategy="afterInteractive"
            />
        </>
    );
};
