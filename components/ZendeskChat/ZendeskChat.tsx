'use client';
import { useUserStore } from '@/store/userStore';
import { useEffect } from 'react';
import axios from 'axios';
import Script from 'next/script';

export const ZendeskChat = () => {
    const { user } = useUserStore();

    useEffect(() => {
        const identifyUser = async () => {
            if (!user) return;

            const waitForZendesk = () => {
                if (typeof window.zE === 'function') {
                    console.log('zendesk loaded');

                    // Use the correct loginUser API with callback function
                    window.zE(
                        'messenger',
                        'loginUser',
                        async (callback: (token: string | null) => void) => {
                            try {
                                const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
                                const { data } = await axios.post('/api/zendesk-token', {
                                    id: user.id,
                                    name: userName || user.email, // Use email as fallback if no name
                                    email: user.email,
                                });
                                callback(data.token);
                            } catch (error) {
                                console.error('Failed to get Zendesk token:', error);
                                callback(null);
                            }
                        },
                        (error: any) => {
                            // Login callback to handle success/failure
                            if (error) {
                                console.error('Zendesk login failed:', error);
                            } else {
                                console.log('Zendesk login successful');

                                // Set conversation fields after successful login
                                // Note: These should be IDs of custom ticket fields created in Zendesk Admin
                                // For now, we'll set conversation tags instead as they don't require pre-configuration
                                const userName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
                                if (userName && window.zE) {
                                    window.zE('messenger:set', 'conversationTags', [
                                        `user:${userName}`,
                                        `email:${user.email}`,
                                    ]);
                                }
                            }
                        }
                    );
                } else {
                    setTimeout(waitForZendesk, 100);
                }
            };

            waitForZendesk();
        };

        if (user) {
            identifyUser();
        }
    }, [user]);

    return (
        <>
            <Script
                id="ze-snippet"
                src="https://static.zdassets.com/ekr/snippet.js?key=bbb92d84-74f1-41d0-a90d-74bd558bbc72"
                strategy="afterInteractive"
            />
        </>
    );
};
